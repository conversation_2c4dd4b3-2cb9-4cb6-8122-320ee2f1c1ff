locals {
  enterprise_apis = {
    "enterprise-account-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/account-cl/health"
      enable_efs       = false #true para habilitar EFS
      efs_mount_path   = "/mnt/shared"
      use_internal_lb = false
    },
    "enterprise-general-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/general-cl/health"
      enable_efs       = false # TRUE para habilitar EFS 
      use_internal_lb = false
    },
    "enterprise-blacklist-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/blacklist-cl/health"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb = false
    },
    "enterprise-webservice-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/ws-enterprise-cl/health"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb = false
    },
    "enterprise-ws-msg-mt-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/ws-msg-mt/health"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb = false
    },
    "enterprise-report-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/report-cl/health"
      enable_efs       = true  # EFS para generar reportes
      efs_mount_path   = "/reports"
      use_internal_lb = false
    },
    "enterprise-maintenance-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/actuator/health"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb = true
    },
    "common-shorturl-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/shorturl/actuator/health"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb  = true
    },
    "common-mailer-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080 # se coloca en 8080 debido a que se usa nginx para validar
      health_check_url = "/api/exampleRequest"
      enable_efs       = false # true para habilitar EFS
      use_internal_lb  = false
    }
  }
}

module "java_api_services" {
  for_each = local.enterprise_apis
  source   = "./modules/api-service"

  name                = each.key
  image               = each.value.image
  cpu                 = each.value.cpu
  memory              = each.value.memory
  application_port    = each.value.ports
  health_check_url    = each.value.health_check_url

  vpc_id              = local.vpc_id
  subnet_ids          = local.subnet_private_ids

  cluster_name        = local.cluster_name
  alb_listener_arn    = lookup(each.value, "use_internal_lb", false) ? local.alb_http_listener_arn : local.albext_https_listener_arn
  #alb_listener_arn    = local.albext_https_listener_arn
  envzone_name        = local.envzone_name
  envzone_zone_id     = local.envzone_zone_id
  alb_dns_name        = lookup(each.value, "use_internal_lb", false) ? local.alb_dns_name : local.albext_dns_name
  #alb_dns_name       = local.albext_dns_name
  private_cidr_block  = "10.0.0.0/8"
  log_retention_days  = local.retention_in_days

  # EFS Configuration (optional per API) revis<r mas adelante lo de efs_mount_path
  enable_efs       = lookup(each.value, "enable_efs", false)
  efs_mount_path   = lookup(each.value, "efs_mount_path", "/reports")
  auto_scaling_policy = lookup(local.auto_scaling_policies, each.key, null)
}


