locals {
  ecr_repositories_all = {
    dev = ["enterprise-account-api" , "enterprise-general-api",
           "enterprise-blacklist-api","enterprise-webservice-api",
           "enterprise-report-api","enterprise-maintenance-api",
           "common-shorturl-api","common-mailer-api","enterprise-ws-msg-mt-api"],
    stg = []
    prd = ["enterprise-account-api" , "enterprise-general-api",
           "enterprise-blacklist-api","enterprise-webservice-api",
           "enterprise-report-api","enterprise-maintenance-api",
           "common-shorturl-api","common-mailer-api","enterprise-ws-msg-mt-api"]
  }
  ecr_repositories = local.ecr_repositories_all[local.env]

  #############################################
  # Auto scaling policies (per environment)
  #############################################
  auto_scaling_policies_all = {
    dev = {
      "enterprise-ws-msg-mt-api" = {
        min          = 1
        max          = 10
        target       = 150
        in_cooldown  = 10
        out_cooldown = 10
      }
      "enterprise-webservice-api" = {
        min          = 1
        max          = 10
        target       = 150
        in_cooldown  = 10
        out_cooldown = 10
      }
    }
    stg = {}
    prd = {}
  }
  auto_scaling_policies = local.auto_scaling_policies_all[local.env]
}



module "ecr" {
  for_each        = toset(local.ecr_repositories)
  source          = "./modules/ecr-repository"
  repository_name = each.key
}